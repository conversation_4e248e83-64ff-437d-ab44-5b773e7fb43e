package com.ximalaya.galaxy.business.repo.service

import com.baomidou.mybatisplus.extension.service.IService
import com.ximalaya.galaxy.business.repo.entity.TrackEntity

/**
 *<AUTHOR>
 *@create 2025-06-02 10:03
 */
interface TrackService : IService<TrackEntity> {

    fun selectByAlbumId(albumId: Long): List<TrackEntity>

    fun selectByUid(uid: Long): List<TrackEntity>

    fun selectBySessionCode(sessionCode: Long): List<TrackEntity>

    fun selectByPhaseCode(phaseCode: Long): List<TrackEntity>

    fun selectByAlbumIdAndUid(albumId: Long, uid: Long): List<TrackEntity>

    fun selectBySessionCodeAndPhaseCode(sessionCode: Long, phaseCode: Long): List<TrackEntity>

    fun createTrack(
        albumId: Long,
        uid: Long,
        sessionCode: Long,
        phaseCode: Long,
        trackName: String,
        trackContent: String? = null
    ): Long

    fun updateTrack(id: Long, trackName: String? = null, trackContent: String? = null): Boolean

    fun deleteTrack(id: Long): Boolean

}
