package com.ximalaya.galaxy.business.repo.service.impl

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import com.ximalaya.galaxy.business.repo.entity.TrackEntity
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.mapper.TrackMapper
import com.ximalaya.galaxy.business.repo.service.TrackService
import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

/**
 *<AUTHOR>
 *@create 2025-06-02 10:03
 */
@Repository
class TrackServiceImpl : ServiceImpl<TrackMapper, TrackEntity>(), TrackService {

    override fun selectByAlbumId(albumId: Long): List<TrackEntity> {
        return ktQuery().eq(TrackEntity::albumId, albumId)
            .eq(TrackEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .orderByDesc(TrackEntity::createTime)
            .list()
    }

    override fun selectByUid(uid: Long): List<TrackEntity> {
        return ktQuery().eq(TrackEntity::uid, uid)
            .eq(TrackEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .orderByDesc(TrackEntity::createTime)
            .list()
    }

    override fun selectBySessionCode(sessionCode: Long): List<TrackEntity> {
        return ktQuery().eq(TrackEntity::sessionCode, sessionCode)
            .eq(TrackEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .orderByDesc(TrackEntity::createTime)
            .list()
    }

    override fun selectByPhaseCode(phaseCode: Long): List<TrackEntity> {
        return ktQuery().eq(TrackEntity::phaseCode, phaseCode)
            .eq(TrackEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .orderByDesc(TrackEntity::createTime)
            .list()
    }

    override fun selectByAlbumIdAndUid(albumId: Long, uid: Long): List<TrackEntity> {
        return ktQuery().eq(TrackEntity::albumId, albumId)
            .eq(TrackEntity::uid, uid)
            .eq(TrackEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .orderByDesc(TrackEntity::createTime)
            .list()
    }

    override fun selectBySessionCodeAndPhaseCode(sessionCode: Long, phaseCode: Long): List<TrackEntity> {
        return ktQuery().eq(TrackEntity::sessionCode, sessionCode)
            .eq(TrackEntity::phaseCode, phaseCode)
            .eq(TrackEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .orderByDesc(TrackEntity::createTime)
            .list()
    }

    override fun createTrack(
        albumId: Long,
        uid: Long,
        sessionCode: Long,
        phaseCode: Long,
        trackName: String,
        trackContent: String?
    ): Long {
        val now = LocalDateTime.now()

        val trackEntity = TrackEntity().apply {
            this.albumId = albumId
            this.uid = uid
            this.sessionCode = sessionCode
            this.phaseCode = phaseCode
            this.trackName = trackName
            this.trackContent = trackContent
            this.deletedAt = LogicDeleted.SAVE.getCode()
            this.createTime = now
            this.updateTime = now
            this.dataCreateTime = now
            this.dataUpdateTime = now
        }

        GalaxyAsserts.assertTrue(save(trackEntity), ErrorCode.UNKNOWN_ERROR, "创建音轨失败")
        return trackEntity.id!!
    }

    override fun updateTrack(id: Long, trackName: String?, trackContent: String?): Boolean {
        val now = LocalDateTime.now()

        val trackEntity = TrackEntity().apply {
            this.id = id
            this.updateTime = now
            this.dataUpdateTime = now

            if (StringUtils.isNotBlank(trackName)) {
                this.trackName = trackName
            }
            if (trackContent != null) {
                this.trackContent = trackContent
            }
        }

        return updateById(trackEntity)
    }

    override fun deleteTrack(id: Long): Boolean {
        val now = LocalDateTime.now()

        val trackEntity = TrackEntity().apply {
            this.id = id
            this.deletedAt = LogicDeleted.DELETED.getCode()
            this.updateTime = now
            this.dataUpdateTime = now
        }

        return updateById(trackEntity)
    }

}
